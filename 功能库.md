总是用中文回复
# Alicres C# 功能库开发规范

## 1. 项目概述

本文档定义了 Alicres 系列 C# 功能库的开发规范，旨在确保代码质量、一致性和可维护性。所有功能库将发布到 NuGet 供个人项目复用。

## 2. 命名约定

### 2.1 项目命名
- **格式**: `Alicres.{功能模块名}`
- **示例**: 
  - `Alicres.SerialPort` - 串口通讯库
  - `Alicres.Database` - 数据库操作库
  - `Alicres.Logging` - 日志记录库

### 2.2 命名空间
- **主命名空间**: `Alicres.{功能模块名}`
- **子命名空间**: `Alicres.{功能模块名}.{子模块}`
- **示例**:
  ```csharp
  namespace Alicres.SerialPort
  namespace Alicres.SerialPort.Models
  namespace Alicres.SerialPort.Exceptions
  ```

### 2.3 类和接口命名
- **类名**: PascalCase，描述性名词
- **接口名**: 以 `I` 开头的 PascalCase
- **抽象类**: 以 `Abstract` 或 `Base` 开头
- **示例**:
  ```csharp
  public interface ISerialPortService
  public class SerialPortService
  public abstract class BaseSerialPortHandler
  ```

### 2.4 方法和属性命名
- **方法名**: PascalCase，动词开头
- **属性名**: PascalCase，名词
- **字段名**: camelCase，私有字段以下划线开头
- **示例**:
  ```csharp
  public void OpenPort()
  public string PortName { get; set; }
  private readonly string _defaultPortName;
  ```

## 3. 项目结构规范

### 3.1 单一解决方案架构
采用统一的解决方案管理所有 Alicres 功能模块，确保代码复用、依赖管理和构建流程的一致性。

```
Alicres/
├── Alicres.sln                     # 主解决方案文件
├── src/                             # 源代码目录
│   ├── Alicres.SerialPort/          # 串口通讯模块
│   │   ├── Interfaces/              # 接口定义
│   │   ├── Models/                  # 数据模型
│   │   ├── Services/                # 核心服务实现
│   │   ├── Exceptions/              # 自定义异常
│   │   ├── Extensions/              # 扩展方法
│   │   ├── Constants/               # 常量定义
│   │   ├── Helpers/                 # 辅助类
│   │   ├── Alicres.SerialPort.csproj
│   │   └── README.md
│   ├── Alicres.Database/            # 数据库操作模块
│   │   ├── Interfaces/
│   │   ├── Models/
│   │   ├── Services/
│   │   ├── Providers/               # 数据库提供程序
│   │   ├── Migrations/              # 数据库迁移
│   │   ├── Alicres.Database.csproj
│   │   └── README.md
│   ├── Alicres.Logging/             # 日志记录模块
│   │   ├── Interfaces/
│   │   ├── Models/
│   │   ├── Services/
│   │   ├── Providers/               # 日志提供程序
│   │   ├── Formatters/              # 日志格式化器
│   │   ├── Alicres.Logging.csproj
│   │   └── README.md
│   ├── Alicres.Common/              # 公共基础库
│   │   ├── Interfaces/              # 通用接口
│   │   ├── Models/                  # 通用模型
│   │   ├── Extensions/              # 通用扩展方法
│   │   ├── Utilities/               # 通用工具类
│   │   ├── Alicres.Common.csproj
│   │   └── README.md
│   └── Alicres.{其他模块}/          # 其他功能模块
├── tests/                           # 测试项目目录
│   ├── Alicres.SerialPort.Tests/
│   │   ├── Services/                # 服务测试
│   │   ├── Models/                  # 模型测试
│   │   ├── TestHelpers/             # 测试辅助
│   │   ├── Fixtures/                # 测试数据
│   │   └── Alicres.SerialPort.Tests.csproj
│   ├── Alicres.Database.Tests/
│   ├── Alicres.Logging.Tests/
│   ├── Alicres.Common.Tests/
│   └── Alicres.IntegrationTests/    # 集成测试
├── examples/                        # 示例项目目录
│   ├── Alicres.SerialPort.Examples/
│   │   ├── Program.cs
│   │   └── Alicres.SerialPort.Examples.csproj
│   ├── Alicres.Database.Examples/
│   ├── Alicres.Logging.Examples/
│   └── Alicres.ComprehensiveExample/ # 综合示例
├── docs/                            # 文档目录
│   ├── api/                         # API 文档
│   ├── guides/                      # 使用指南
│   ├── tutorials/                   # 教程文档
│   └── architecture/                # 架构文档
├── tools/                           # 构建工具和脚本
│   ├── build/                       # 构建脚本
│   ├── deploy/                      # 部署脚本
│   └── generators/                  # 代码生成器
├── .github/                         # GitHub 配置
│   ├── workflows/                   # CI/CD 工作流
│   └── ISSUE_TEMPLATE/              # 问题模板
├── .gitignore                       # Git 忽略文件
├── .editorconfig                    # 编辑器配置
├── Directory.Build.props            # 全局构建属性
├── Directory.Build.targets          # 全局构建目标
├── Directory.Packages.props         # 中央包管理
├── nuget.config                     # NuGet 配置
├── global.json                      # .NET SDK 版本
├── README.md                        # 项目主文档
└── CHANGELOG.md                     # 变更日志
```

### 3.2 模块化设计原则
- **功能独立性**：每个模块专注于特定功能领域，避免功能重叠
- **依赖最小化**：模块间依赖关系清晰，避免循环依赖
- **接口优先**：通过接口定义模块边界，支持依赖注入
- **公共基础**：Alicres.Common 提供所有模块共享的基础功能

### 3.3 项目文件配置标准
每个项目的 `.csproj` 文件应包含：
- 目标框架（推荐 .NET 8.0 及以上）
- 包信息（作者、描述、版本等）
- 文档生成配置
- 代码分析规则
- 项目间引用关系

## 4. 代码风格指南

### 4.1 基本原则
- 遵循 Microsoft C# 编码约定
- 使用 EditorConfig 统一代码格式
- 启用代码分析和警告处理

### 4.2 代码组织
- 每个文件只包含一个公共类型
- 使用 `using` 语句进行资源管理
- 优先使用表达式体成员（适当时）
- 合理使用异步编程模式

### 4.3 错误处理
- 使用自定义异常类型
- 提供有意义的错误消息
- 记录异常信息用于调试

## 5. 文档注释规范

### 5.1 XML 文档注释
所有公共成员必须包含 XML 文档注释：

```csharp
/// <summary>
/// 串口通讯服务接口，提供串口的基本操作功能
/// </summary>
public interface ISerialPortService
{
    /// <summary>
    /// 打开指定的串口
    /// </summary>
    /// <param name="portName">串口名称，如 "COM1"</param>
    /// <param name="baudRate">波特率，默认为 9600</param>
    /// <returns>如果成功打开返回 true，否则返回 false</returns>
    /// <exception cref="ArgumentException">当端口名称无效时抛出</exception>
    /// <exception cref="InvalidOperationException">当端口已被占用时抛出</exception>
    Task<bool> OpenPortAsync(string portName, int baudRate = 9600);
}
```

### 5.2 README 文档
每个项目必须包含详细的 README.md：
- 功能描述
- 安装说明
- 使用示例
- API 文档链接
- 贡献指南

## 6. 单元测试要求

### 6.1 测试覆盖率
- 代码覆盖率目标：≥ 80%
- 所有公共方法必须有对应测试
- 包含正常流程和异常情况测试

### 6.2 测试框架
- 使用 xUnit 作为测试框架
- 使用 Moq 进行模拟对象创建
- 使用 FluentAssertions 进行断言

### 6.3 测试命名约定
```csharp
[Fact]
public void OpenPort_WithValidPortName_ShouldReturnTrue()
{
    // Arrange
    // Act  
    // Assert
}
```

## 7. NuGet 包发布规范

### 7.1 包信息配置
```xml
<PropertyGroup>
    <PackageId>Alicres.SerialPort</PackageId>
    <Version>1.0.0</Version>
    <Authors>Alicres</Authors>
    <Description>A comprehensive serial port communication library for .NET</Description>
    <PackageTags>serialport;communication;hardware</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/alicres/Alicres.SerialPort</PackageProjectUrl>
    <RepositoryUrl>https://github.com/alicres/Alicres.SerialPort</RepositoryUrl>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
</PropertyGroup>
```

### 7.2 发布流程
1. 更新版本号
2. 运行所有测试
3. 生成发布包
4. 发布到 NuGet.org

## 8. 版本管理策略

### 8.1 语义化版本控制
采用 SemVer 2.0.0 规范：
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 8.2 分支策略
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 9. 构建配置与依赖管理

### 9.1 全局构建配置
使用 `Directory.Build.props` 统一管理所有项目的构建属性：

```xml
<Project>
  <PropertyGroup>
    <!-- 目标框架 -->
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- 包信息 -->
    <Authors>Alicres</Authors>
    <Company>Alicres</Company>
    <Copyright>Copyright © Alicres 2024</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://github.com/alicres/Alicres</RepositoryUrl>

    <!-- 文档生成 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>

    <!-- 代码分析 -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>

    <!-- 包生成 -->
    <GeneratePackageOnBuild Condition="'$(Configuration)' == 'Release'">true</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
</Project>
```

### 9.2 中央包管理
使用 `Directory.Packages.props` 统一管理包版本：

```xml
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <!-- 测试框架 -->
    <PackageVersion Include="xunit" Version="2.4.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="Moq" Version="4.20.69" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />

    <!-- 日志记录 -->
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Serilog" Version="3.1.1" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="8.0.0" />

    <!-- 依赖注入 -->
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="8.0.0" />

    <!-- 配置管理 -->
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="8.0.0" />

    <!-- 序列化 -->
    <PackageVersion Include="System.Text.Json" Version="8.0.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>
```

### 9.3 依赖管理原则
- **最小化外部依赖**：优先使用 .NET 标准库和 Microsoft 官方包
- **版本一致性**：通过中央包管理确保所有项目使用相同版本
- **安全性考虑**：定期更新依赖包，及时修复安全漏洞
- **兼容性验证**：新增依赖前验证与现有包的兼容性

### 9.4 模块间依赖关系
```
Alicres.Common (基础库)
    ↑
    ├── Alicres.SerialPort
    ├── Alicres.Database
    ├── Alicres.Logging
    └── Alicres.{其他模块}
```

- **Alicres.Common**：提供所有模块共享的基础功能
- **功能模块**：可以依赖 Common，但模块间不应直接依赖
- **示例项目**：可以依赖任意功能模块进行演示

## 10. 渐进式开发流程

### 10.1 开发流程原则
借鉴优秀的渐进式迭代设计理念，采用"功能模块逐个实现、质量门禁逐步验证"的开发方法：

- **模块优先级驱动**：按 P0→P1→P2 优先级逐个开发功能模块
- **功能完整性验证**：每个模块完成后进行完整的功能测试
- **质量门禁机制**：每个阶段完成后暂停等待代码审查和测试验证
- **用户反馈循环**：定期收集使用反馈，持续优化和改进

### 10.2 模块开发优先级
```markdown
## P0 级核心模块（必须优先完成）
- **Alicres.Common**：基础公共库，提供所有模块的共享功能
- **Alicres.Logging**：日志记录模块，支持调试和监控
- **核心业务模块**：根据具体需求确定的核心功能模块

## P1 级重要模块（次要优先级）
- **Alicres.Database**：数据库操作模块，支持多种数据库
- **Alicres.SerialPort**：串口通讯模块，硬件交互功能
- **其他重要业务模块**：显著提升用户体验的功能

## P2 级增值模块（最后完成）
- **高级功能模块**：提供额外价值的增值功能
- **扩展集成模块**：与第三方系统的集成功能
- **性能优化模块**：性能监控和优化工具
```

### 10.3 质量门禁标准
每个模块完成后必须通过以下质量检查：

#### 10.3.1 代码质量门禁
- ✅ **代码覆盖率**：单元测试覆盖率 ≥ 80%
- ✅ **代码分析**：无严重警告和错误
- ✅ **代码审查**：通过同行代码审查
- ✅ **文档完整性**：API 文档和使用说明完整

#### 10.3.2 功能质量门禁
- ✅ **单元测试**：所有公共方法有对应测试
- ✅ **集成测试**：模块间集成功能正常
- ✅ **性能测试**：满足性能要求
- ✅ **兼容性测试**：跨平台兼容性验证

#### 10.3.3 发布质量门禁
- ✅ **包构建**：NuGet 包成功构建
- ✅ **版本管理**：版本号符合语义化规范
- ✅ **变更日志**：更新详细的变更记录
- ✅ **示例验证**：示例代码运行正常

### 10.4 持续集成配置
使用 GitHub Actions 实现自动化 CI/CD：

```yaml
# .github/workflows/ci.yml
name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Test
      run: dotnet test --no-build --configuration Release --collect:"XPlat Code Coverage"

    - name: Code Coverage
      uses: codecov/codecov-action@v3

    - name: Pack NuGet packages
      run: dotnet pack --no-build --configuration Release --output ./packages

    - name: Upload packages
      uses: actions/upload-artifact@v4
      with:
        name: nuget-packages
        path: ./packages/*.nupkg
```

### 10.5 发布流程
1. **版本规划**：确定发布版本和功能范围
2. **质量验证**：执行完整的质量门禁检查
3. **文档更新**：更新 API 文档和使用指南
4. **包发布**：发布到 NuGet.org
5. **发布通知**：更新 GitHub Release 和变更日志

## 11. 项目治理与维护

### 11.1 代码贡献规范
- **分支策略**：使用 GitFlow 工作流
- **提交规范**：遵循 Conventional Commits 规范
- **代码审查**：所有代码变更必须经过审查
- **问题跟踪**：使用 GitHub Issues 跟踪问题和功能请求

### 11.2 文档维护
- **API 文档**：自动生成并保持最新
- **使用指南**：提供详细的使用说明和最佳实践
- **示例代码**：维护可运行的示例项目
- **架构文档**：记录重要的架构决策和设计理念

### 11.3 社区支持
- **问题响应**：及时响应用户问题和反馈
- **功能请求**：评估和实现有价值的功能请求
- **版本兼容性**：维护向后兼容性，谨慎处理破坏性变更
- **安全更新**：及时修复安全漏洞和问题

---

*本文档将随着项目发展持续更新和完善。遵循渐进式开发理念，确保每个功能模块都达到高质量标准。*